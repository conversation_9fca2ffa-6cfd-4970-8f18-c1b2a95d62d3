import React from 'react'

export default destructuring
// Array destructuring (không dùng)
var myDate = [20, 8, 2025];

var day = myDate[0];
var month = myDate[1];
var year = myDate[2];

// array destructuring (dùng)
var [myDay, myMonth, myYear] = myDate;
console.log("day", day, myDay);
console.log("month", month, myMonth);
console.log("year", year, myYear);

var person = {
  name: "hoang", // Tên: hoang
  age: 21, // Tuổi: 21
};
var name = person.name; // <PERSON><PERSON> thuộc tính name vào biến name
var age = person.age; // <PERSON><PERSON> thuộc tính age vào biến age
console.log(name); // In: hoang
console.log(age); // In: 21

// Object destructuring (dùng)
var { name, age } = person; // Phân rã object person1 vào các biến name1, age
console.log(name);
console.log(age);

const color = ["red", "blue", "green"];
const [red, blue, green] = color;
console.log(red, blue, green);

const UserProfile = ({ name, age }) => (
  <h1>
    {name} is {age} years old
  </h1>
);
function UserProfile({ name, age }) {
  return (
    <h1>
      {name} is {age} years old
    </h1>
  );
}


